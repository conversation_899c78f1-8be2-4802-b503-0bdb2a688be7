/**
 * API调用工具类
 * 封装uniCloud云函数调用
 */

export interface CloudFunctionResult<T = any> {
  result: T
  requestID: string
  errMsg: string
}

export interface TaskInfo {
  _id: string
  _openid: string
  originalVideoFileId: string
  processedVideoFileId?: string
  status: 'processing' | 'completed' | 'failed'
  errorMsg?: string
  createTime: string
  finishTime?: string
  fileName?: string
  fileSize?: number
  duration?: number
}

/**
 * 云函数调用封装 - 使用uniCloud
 */
export const callCloudFunction = <T = any>(
  name: string,
  data?: any
): Promise<T> => {
  return new Promise((resolve, reject) => {
    // 使用uniCloud调用云函数
    uniCloud.callFunction({
      name,
      data,
      success: (res: any) => {
        resolve(res.result)
      },
      fail: (error) => {
        console.error(`云函数 ${name} 调用失败:`, error)
        reject(error)
      }
    })
  })
}

/**
 * 获取上传签名
 */
export const getUploadSignature = (): Promise<string> => {
  return callCloudFunction('get-upload-signature')
}

/**
 * 创建任务记录
 */
export const createTask = (fileId: string): Promise<TaskInfo> => {
  const deviceId = getDeviceId()
  return callCloudFunction('create-task', { fileId, deviceId })
}

/**
 * 获取任务状态
 */
export const getTaskStatus = (fileId: string): Promise<TaskInfo> => {
  return callCloudFunction('get-task-status', { fileId })
}

/**
 * 获取任务详情
 */
export const getTaskDetail = (taskId: string): Promise<TaskInfo> => {
  return callCloudFunction('get-task-detail', { taskId })
}

/**
 * 生成设备唯一标识
 */
export const generateDeviceId = (): string => {
  try {
    const systemInfo = uni.getSystemInfoSync()
    const deviceInfo = {
      platform: systemInfo.platform,
      model: systemInfo.model,
      system: systemInfo.system,
      version: systemInfo.version,
      timestamp: Date.now()
    }

    // 生成设备指纹
    const deviceFingerprint = btoa(JSON.stringify(deviceInfo))

    // 存储到本地
    uni.setStorageSync('deviceId', deviceFingerprint)

    return deviceFingerprint
  } catch (error) {
    console.error('生成设备ID失败:', error)
    // 降级方案：使用时间戳
    const fallbackId = 'device_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
    uni.setStorageSync('deviceId', fallbackId)
    return fallbackId
  }
}

/**
 * 获取设备ID
 */
export const getDeviceId = (): string => {
  let deviceId = uni.getStorageSync('deviceId')
  if (!deviceId) {
    deviceId = generateDeviceId()
  }
  return deviceId
}

/**
 * 获取视频播放URL
 */
export const getVideoUrl = (fileId: string): Promise<{ url: string; poster?: string }> => {
  return callCloudFunction('get-video-url', { fileId })
}

/**
 * 获取字幕内容
 */
export const getSubtitle = (taskId: string): Promise<Array<{
  startTime: string
  endTime: string
  text: string
}>> => {
  return callCloudFunction('get-subtitle', { taskId })
}

/**
 * 获取字幕文件
 */
export const getSubtitleFile = (taskId: string): Promise<{ url: string }> => {
  return callCloudFunction('get-subtitle-file', { taskId })
}

/**
 * 获取历史记录列表
 */
export const getHistoryList = (skip = 0, limit = 20): Promise<TaskInfo[]> => {
  return callCloudFunction('get-history-list', { skip, limit })
}

/**
 * 获取最近任务
 */
export const getRecentTasks = (limit = 3): Promise<TaskInfo[]> => {
  return callCloudFunction('get-recent-tasks', { limit })
}

/**
 * 重新处理任务
 */
export const retryTask = (taskId: string): Promise<void> => {
  return callCloudFunction('retry-task', { taskId })
}

/**
 * 删除任务
 */
export const deleteTask = (taskId: string): Promise<void> => {
  return callCloudFunction('delete-task', { taskId })
}
