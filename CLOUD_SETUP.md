# 云开发配置指南

## 项目架构说明

本项目使用 **uni-app + 微信小程序云开发** 的架构：
- 前端：uni-app框架开发，编译为微信小程序
- 后端：微信小程序云开发服务
- 视频处理：腾讯云VOD服务

## 解决"应用未关联服务空间"错误

### 方案一：使用微信小程序云开发（推荐）

1. **在微信开发者工具中开通云开发**：
   ```
   1. 打开微信开发者工具
   2. 导入项目：dist/dev/mp-weixin
   3. 点击工具栏的"云开发"按钮
   4. 按照提示开通云开发服务
   5. 创建云环境（建议创建开发环境和生产环境）
   ```

2. **配置云环境ID**：
   在 `src/main.ts` 中填入你的云环境ID：
   ```typescript
   wx.cloud.init({
     env: 'your-env-id', // 替换为实际的云环境ID
     traceUser: true
   })
   ```

3. **部署云函数**：
   ```
   1. 在微信开发者工具中，右键点击 cloudfunctions 文件夹
   2. 选择"上传并部署：云端安装依赖"
   3. 等待部署完成
   ```

4. **创建数据库集合**：
   ```
   1. 在云开发控制台中，进入数据库
   2. 创建集合：tasks
   3. 设置权限：仅创建者可读写
   ```

### 方案二：使用uniCloud（备选）

如果您希望使用uniCloud，需要进行以下调整：

1. **创建uniCloud项目**：
   ```
   1. 在HBuilderX中打开项目
   2. 右键点击 uniCloud-aliyun 文件夹
   3. 选择"关联云服务空间"
   4. 创建或选择云服务空间
   ```

2. **修改API调用**：
   将 `src/utils/api.ts` 中的 `wx.cloud` 改为 `uniCloud`

## 当前配置状态

✅ **已配置**：
- uni-app项目结构
- 微信小程序云开发API调用
- 云函数模板
- 数据库结构设计

⏳ **需要手动配置**：
- 微信小程序AppID
- 云开发环境ID
- 腾讯云VOD配置
- 云函数部署

## 配置步骤详解

### 1. 微信小程序配置

在 `src/manifest.json` 中设置AppID：
```json
{
  "mp-weixin": {
    "appid": "你的小程序AppID"
  }
}
```

### 2. 云开发初始化

项目已在 `src/main.ts` 中配置云开发初始化代码，只需填入环境ID。

### 3. 云函数部署

项目提供了以下云函数模板：
- `get-upload-signature`: 获取VOD上传签名
- `create-task`: 创建任务记录

### 4. 数据库配置

需要创建 `tasks` 集合，字段结构见 `uniCloud-aliyun/database/db_init.json`

## 测试验证

配置完成后，可以通过以下方式验证：

1. **编译测试**：
   ```bash
   npm run dev:mp-weixin
   ```

2. **功能测试**：
   - 在微信开发者工具中预览
   - 测试页面导航
   - 测试云函数调用（需要先部署）

## 常见问题

### Q: 提示"应用未关联服务空间"
A: 这是因为项目使用微信小程序云开发，不是uniCloud。请按照方案一配置。

### Q: 云函数调用失败
A: 确保已在微信开发者工具中部署云函数，并且云环境ID配置正确。

### Q: 编译时有警告
A: VOD SDK使用了eval，这是正常现象，不影响功能。

## 技术支持

如需帮助，请参考：
- [微信小程序云开发文档](https://developers.weixin.qq.com/miniprogram/dev/wxcloud/basis/getting-started.html)
- [uni-app官方文档](https://uniapp.dcloud.net.cn/)
- [腾讯云VOD文档](https://cloud.tencent.com/document/product/266)

---

配置完成后，项目即可正常运行！🚀
