<template>
  <view class="task-status">
    <!-- 状态图标和文本 -->
    <view class="status-header">
      <view class="status-icon" :class="status">
        <text v-if="status === 'processing'" class="icon processing">⏳</text>
        <text v-else-if="status === 'completed'" class="icon success">✅</text>
        <text v-else-if="status === 'failed'" class="icon error">❌</text>
        <text v-else class="icon pending">⏸️</text>
      </view>
      
      <view class="status-text">
        <text class="status-title">{{ statusTitle }}</text>
        <text class="status-desc">{{ statusDesc }}</text>
      </view>
    </view>
    
    <!-- 进度条 -->
    <view v-if="showProgress" class="progress-section">
      <view class="progress-bar">
        <view 
          class="progress-fill" 
          :style="{ width: progressPercent + '%' }"
        ></view>
      </view>
      <text class="progress-text">{{ progressText }}</text>
    </view>
    
    <!-- 处理步骤 -->
    <view v-if="showSteps" class="steps-section">
      <view 
        v-for="(step, index) in steps" 
        :key="index"
        class="step-item"
        :class="{ 
          active: currentStep >= index + 1, 
          completed: currentStep > index + 1 
        }"
      >
        <view class="step-indicator">
          <view class="step-number">{{ index + 1 }}</view>
          <view v-if="index < steps.length - 1" class="step-line"></view>
        </view>
        
        <view class="step-content">
          <text class="step-title">{{ step.title }}</text>
          <text class="step-desc">{{ step.desc }}</text>
          <text v-if="step.time" class="step-time">{{ step.time }}</text>
        </view>
      </view>
    </view>
    
    <!-- 错误信息 -->
    <view v-if="errorMsg" class="error-section">
      <text class="error-title">错误详情</text>
      <text class="error-message">{{ errorMsg }}</text>
    </view>
    
    <!-- 操作按钮 -->
    <view v-if="showActions" class="actions-section">
      <slot name="actions">
        <button 
          v-if="status === 'completed'" 
          @click="$emit('view-result')"
          class="action-btn primary"
        >
          查看结果
        </button>
        
        <button 
          v-if="status === 'failed'" 
          @click="$emit('retry')"
          class="action-btn primary"
        >
          重新处理
        </button>
        
        <button 
          v-if="status === 'processing'" 
          @click="$emit('cancel')"
          class="action-btn secondary"
        >
          取消处理
        </button>
      </slot>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { getStatusText, formatTime } from '@/utils/common'

// Props
interface Step {
  title: string
  desc: string
  time?: string
}

interface Props {
  status: 'pending' | 'processing' | 'completed' | 'failed'
  currentStep?: number
  progress?: number
  errorMsg?: string
  showProgress?: boolean
  showSteps?: boolean
  showActions?: boolean
  steps?: Step[]
  customTitle?: string
  customDesc?: string
}

const props = withDefaults(defineProps<Props>(), {
  currentStep: 0,
  progress: 0,
  showProgress: false,
  showSteps: true,
  showActions: true,
  steps: () => [
    { title: '视频上传', desc: '视频文件上传到云端' },
    { title: '语音识别', desc: 'AI识别视频中的语音内容' },
    { title: '字幕生成', desc: '生成SRT字幕文件' },
    { title: '视频合成', desc: '将字幕烧录到视频中' }
  ]
})

// Emits
interface Emits {
  (e: 'view-result'): void
  (e: 'retry'): void
  (e: 'cancel'): void
}

const emit = defineEmits<Emits>()

// 计算属性
const statusTitle = computed(() => {
  if (props.customTitle) return props.customTitle
  
  const titleMap: Record<string, string> = {
    'pending': '等待处理',
    'processing': '正在处理中...',
    'completed': '处理完成',
    'failed': '处理失败'
  }
  
  return titleMap[props.status] || '未知状态'
})

const statusDesc = computed(() => {
  if (props.customDesc) return props.customDesc
  
  const descMap: Record<string, string> = {
    'pending': '任务已创建，等待开始处理',
    'processing': '请耐心等待，通常需要1-3分钟',
    'completed': '视频字幕已生成完成',
    'failed': '处理过程中出现错误，请重试'
  }
  
  return descMap[props.status] || ''
})

const progressPercent = computed(() => {
  if (props.progress > 0) return props.progress
  
  // 根据当前步骤计算进度
  if (props.currentStep > 0 && props.steps.length > 0) {
    return Math.round((props.currentStep / props.steps.length) * 100)
  }
  
  return 0
})

const progressText = computed(() => {
  if (props.status === 'processing') {
    return `处理进度 ${progressPercent.value}%`
  }
  return ''
})
</script>

<style scoped>
.task-status {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
}

.status-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.status-icon {
  margin-right: 30rpx;
}

.icon {
  font-size: 80rpx;
  display: block;
}

.icon.processing {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.icon.success {
  color: #52c41a;
}

.icon.error {
  color: #ff4d4f;
}

.icon.pending {
  color: #faad14;
}

.status-text .status-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.status-text .status-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
}

.progress-section {
  margin-bottom: 30rpx;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 15rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007AFF, #40a9ff);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 26rpx;
  color: #666;
  text-align: center;
  display: block;
}

.steps-section {
  margin-bottom: 30rpx;
}

.step-item {
  display: flex;
  margin-bottom: 30rpx;
  position: relative;
}

.step-item:last-child {
  margin-bottom: 0;
}

.step-indicator {
  position: relative;
  margin-right: 30rpx;
}

.step-number {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #f0f0f0;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  position: relative;
  z-index: 2;
}

.step-item.active .step-number {
  background-color: #007AFF;
  color: white;
}

.step-item.completed .step-number {
  background-color: #52c41a;
  color: white;
}

.step-line {
  position: absolute;
  top: 60rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 2rpx;
  height: 50rpx;
  background-color: #f0f0f0;
}

.step-item.active .step-line,
.step-item.completed .step-line {
  background-color: #007AFF;
}

.step-content {
  flex: 1;
  padding-top: 8rpx;
}

.step-content .step-title {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.step-content .step-desc {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 5rpx;
}

.step-content .step-time {
  display: block;
  font-size: 22rpx;
  color: #999;
}

.error-section {
  background-color: #fff2f0;
  border: 2rpx solid #ffccc7;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 30rpx;
}

.error-title {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #ff4d4f;
  margin-bottom: 10rpx;
}

.error-message {
  display: block;
  font-size: 24rpx;
  color: #ff4d4f;
  line-height: 1.4;
}

.actions-section {
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.action-btn.primary {
  background-color: #007AFF;
  color: white;
  border: none;
}

.action-btn.secondary {
  background-color: white;
  color: #666;
  border: 2rpx solid #d9d9d9;
}
</style>
