// uniCloud云函数：获取历史记录列表
'use strict';

/**
 * 获取用户的历史任务记录列表
 */
exports.main = async (event, context) => {
  try {
    const { skip = 0, limit = 20 } = event
    
    // 获取数据库引用
    const db = uniCloud.database()
    
    // 查询历史记录
    const result = await db.collection('tasks')
      .orderBy('createTime', 'desc')
      .skip(skip)
      .limit(limit)
      .get()
    
    return {
      errCode: 0,
      errMsg: '查询成功',
      data: result.data,
      total: result.data.length
    }
    
  } catch (error) {
    console.error('查询历史记录失败:', error)
    return {
      errCode: 'QUERY_HISTORY_FAILED',
      errMsg: '查询历史记录失败: ' + error.message
    }
  }
}
