<template>
  <view class="video-uploader">
    <!-- 上传区域 -->
    <view 
      class="upload-area" 
      :class="{ 'drag-over': isDragOver }"
      @click="chooseVideo"
    >
      <view v-if="!videoInfo" class="upload-placeholder">
        <text class="upload-icon">📹</text>
        <text class="upload-title">点击选择视频</text>
        <text class="upload-desc">支持MP4、MOV等格式，最大{{ maxSizeMB }}MB</text>
      </view>
      
      <!-- 视频预览 -->
      <view v-else class="video-preview">
        <video 
          :src="videoInfo.tempFilePath" 
          :poster="videoInfo.thumbTempFilePath"
          class="preview-video"
          controls
        ></video>
        <view class="video-meta">
          <text class="file-name">{{ videoInfo.name || '未命名视频' }}</text>
          <view class="file-info">
            <text>大小: {{ formatFileSize(videoInfo.size) }}</text>
            <text>时长: {{ formatDuration(videoInfo.duration) }}</text>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 上传进度 -->
    <view v-if="uploading" class="upload-progress">
      <view class="progress-header">
        <text class="progress-title">上传进度</text>
        <text class="progress-percent">{{ uploadPercent }}%</text>
      </view>
      <view class="progress-bar">
        <view 
          class="progress-fill" 
          :style="{ width: uploadPercent + '%' }"
        ></view>
      </view>
      <text class="progress-status">{{ uploadStatus }}</text>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button 
        v-if="videoInfo && !uploading" 
        @click="startUpload"
        class="upload-btn"
        type="primary"
        :disabled="!canUpload"
      >
        {{ uploadBtnText }}
      </button>
      
      <button 
        v-if="videoInfo && !uploading" 
        @click="resetVideo"
        class="reset-btn"
      >
        重新选择
      </button>
      
      <button 
        v-if="uploading" 
        @click="cancelUpload"
        class="cancel-btn"
      >
        取消上传
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import VodUploader from 'vod-wx-sdk-v2'
import { formatFileSize, formatDuration, validateVideoFile, handleError, showSuccess } from '@/utils/common'
import { getUploadSignature, createTask } from '@/utils/api'

// Props
interface Props {
  maxSizeMB?: number
  maxDuration?: number
  autoUpload?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  maxSizeMB: 100,
  maxDuration: 300,
  autoUpload: false
})

// Emits
interface Emits {
  (e: 'upload-start', fileInfo: any): void
  (e: 'upload-progress', percent: number): void
  (e: 'upload-success', result: any): void
  (e: 'upload-error', error: any): void
  (e: 'video-selected', videoInfo: any): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const videoInfo = ref<any>(null)
const uploading = ref(false)
const uploadPercent = ref(0)
const uploadStatus = ref('')
const isDragOver = ref(false)
let uploader: any = null

// 计算属性
const canUpload = computed(() => {
  if (!videoInfo.value) return false
  const validation = validateVideoFile(videoInfo.value)
  return validation.valid
})

const uploadBtnText = computed(() => {
  if (!canUpload.value) return '文件不符合要求'
  return '开始上传'
})

// 选择视频
const chooseVideo = () => {
  if (uploading.value) return
  
  uni.chooseVideo({
    sourceType: ['camera', 'album'],
    maxDuration: props.maxDuration,
    success: (res) => {
      console.log('选择视频成功:', res)
      
      // 验证文件
      const validation = validateVideoFile(res)
      if (!validation.valid) {
        handleError(validation.message)
        return
      }
      
      videoInfo.value = {
        ...res,
        name: `video_${Date.now()}.mp4`
      }
      
      emit('video-selected', videoInfo.value)
      
      // 自动上传
      if (props.autoUpload) {
        startUpload()
      }
    },
    fail: (err) => {
      console.error('选择视频失败:', err)
      handleError('选择视频失败')
    }
  })
}

// 开始上传
const startUpload = async () => {
  if (!videoInfo.value || uploading.value) return
  
  try {
    uploading.value = true
    uploadPercent.value = 0
    uploadStatus.value = '准备上传...'
    
    emit('upload-start', videoInfo.value)
    
    // 获取上传签名
    uploadStatus.value = '获取上传凭证...'
    const signature = await getUploadSignature()
    
    // 开始上传
    uploadStatus.value = '上传中...'
    uploader = VodUploader.start({
      mediaFile: videoInfo.value,
      getSignature: () => signature,
      procedure: 'SubtitleProcessing', // 任务流名称
      onProgress: (progress: any) => {
        uploadPercent.value = Math.round(progress.percent * 100)
        emit('upload-progress', uploadPercent.value)
      },
      onFinish: async (result: any) => {
        console.log('上传完成:', result)
        uploadStatus.value = '创建任务记录...'
        
        try {
          // 创建任务记录
          await createTask(result.fileId)
          
          uploading.value = false
          showSuccess('上传成功')
          emit('upload-success', {
            ...result,
            videoInfo: videoInfo.value
          })
          
          // 重置状态
          resetVideo()
        } catch (error) {
          console.error('创建任务失败:', error)
          uploading.value = false
          emit('upload-error', error)
          handleError('创建任务失败')
        }
      },
      onError: (error: any) => {
        console.error('上传失败:', error)
        uploading.value = false
        emit('upload-error', error)
        handleError('上传失败')
      }
    })
  } catch (error) {
    console.error('上传过程出错:', error)
    uploading.value = false
    emit('upload-error', error)
    handleError('上传失败')
  }
}

// 取消上传
const cancelUpload = () => {
  if (uploader) {
    uploader.cancel()
    uploader = null
  }
  
  uploading.value = false
  uploadPercent.value = 0
  uploadStatus.value = ''
}

// 重置视频
const resetVideo = () => {
  videoInfo.value = null
  uploading.value = false
  uploadPercent.value = 0
  uploadStatus.value = ''
  
  if (uploader) {
    uploader = null
  }
}

// 暴露方法给父组件
defineExpose({
  startUpload,
  cancelUpload,
  resetVideo,
  videoInfo: readonly(videoInfo),
  uploading: readonly(uploading)
})
</script>

<style scoped>
.video-uploader {
  width: 100%;
}

.upload-area {
  border: 2rpx dashed #d9d9d9;
  border-radius: 12rpx;
  background-color: #fafafa;
  transition: all 0.3s ease;
  overflow: hidden;
}

.upload-area:hover,
.upload-area.drag-over {
  border-color: #007AFF;
  background-color: #f0f8ff;
}

.upload-placeholder {
  padding: 80rpx 40rpx;
  text-align: center;
}

.upload-icon {
  display: block;
  font-size: 120rpx;
  margin-bottom: 20rpx;
  color: #999;
}

.upload-title {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
  font-weight: 500;
}

.upload-desc {
  display: block;
  font-size: 26rpx;
  color: #999;
}

.video-preview {
  padding: 20rpx;
}

.preview-video {
  width: 100%;
  height: 400rpx;
  border-radius: 8rpx;
  background-color: #000;
}

.video-meta {
  margin-top: 20rpx;
}

.file-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.file-info {
  display: flex;
  justify-content: space-between;
  font-size: 24rpx;
  color: #666;
}

.upload-progress {
  margin-top: 30rpx;
  padding: 30rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.progress-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.progress-percent {
  font-size: 28rpx;
  color: #007AFF;
  font-weight: bold;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background-color: #e9ecef;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 15rpx;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007AFF, #40a9ff);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

.progress-status {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  display: block;
}

.action-buttons {
  margin-top: 30rpx;
  display: flex;
  gap: 20rpx;
}

.upload-btn {
  flex: 1;
  height: 88rpx;
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.upload-btn:disabled {
  background-color: #d9d9d9;
  color: #999;
}

.reset-btn, .cancel-btn {
  flex: 1;
  height: 88rpx;
  background-color: white;
  color: #666;
  border: 2rpx solid #d9d9d9;
  border-radius: 8rpx;
  font-size: 32rpx;
}

.cancel-btn {
  color: #ff4d4f;
  border-color: #ff4d4f;
}
</style>
