# 微信小程序云开发迁移到uniCloud阿里云指南

## 迁移概述

本项目已从微信小程序云开发迁移到uniCloud阿里云serverless平台。本文档记录了迁移过程中的关键变更和注意事项。

## 主要变更

### 1. 云开发初始化

**迁移前（微信云开发）：**
```javascript
// src/main.ts
if (typeof wx !== 'undefined' && wx.cloud) {
  wx.cloud.init({
    env: 'your-env-id',
    traceUser: true
  })
}
```

**迁移后（uniCloud）：**
```javascript
// src/main.ts
// uniCloud会自动根据项目配置连接到对应的服务空间
// 无需手动初始化，uniCloud会在第一次调用时自动初始化
```

### 2. API调用方式

**迁移前（微信云开发）：**
```javascript
wx.cloud.callFunction({
  name: 'functionName',
  data: {},
  success: (res) => {},
  fail: (error) => {}
})
```

**迁移后（uniCloud）：**
```javascript
uniCloud.callFunction({
  name: 'functionName',
  data: {},
  success: (res) => {},
  fail: (error) => {}
})
```

### 3. 云函数结构

**迁移前（微信云开发）：**
```javascript
// cloudfunctions/function-name/index.js
const cloud = require('wx-server-sdk')
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })

exports.main = async (event, context) => {
  const { OPENID } = cloud.getWXContext()
  // ...
}
```

**迁移后（uniCloud）：**
```javascript
// uniCloud-aliyun/cloudfunctions/function-name/index.js
'use strict';

exports.main = async (event, context) => {
  const { CLIENTUA, CLIENTIP } = context
  // ...
}
```

### 4. 数据库操作

**迁移前（微信云开发）：**
```javascript
const cloud = require('wx-server-sdk')
const db = cloud.database()
```

**迁移后（uniCloud）：**
```javascript
const db = uniCloud.database()
```

### 5. 错误处理规范

**迁移后统一使用uniCloud响应体规范：**
```javascript
// 成功返回
{
  errCode: 0,
  errMsg: '操作成功',
  data: {}
}

// 失败返回
{
  errCode: 'ERROR_CODE',
  errMsg: '错误描述'
}
```

## 云函数迁移清单

### 已迁移的云函数

- [x] `create-task` - 创建任务记录
- [x] `get-upload-signature` - 获取VOD上传签名
- [x] `get-task-status` - 获取任务状态
- [x] `get-history-list` - 获取历史记录列表

### 待创建的云函数

- [ ] `get-task-detail` - 获取任务详情
- [ ] `get-video-url` - 获取视频播放URL
- [ ] `get-subtitle` - 获取字幕内容
- [ ] `get-subtitle-file` - 获取字幕文件
- [ ] `get-recent-tasks` - 获取最近任务
- [ ] `retry-task` - 重新处理任务
- [ ] `delete-task` - 删除任务

## 数据库迁移

### 数据表结构

- **表名**: `tasks`
- **描述**: 视频处理任务记录
- **Schema文件**: `uniCloud-aliyun/database/tasks.schema.json`

### 权限配置

- 读取权限：开放
- 创建权限：开放
- 更新权限：关闭
- 删除权限：关闭

## 配置要求

### 环境变量（需在uniCloud控制台配置）

- `TENCENT_SECRET_ID` - 腾讯云API密钥ID
- `TENCENT_SECRET_KEY` - 腾讯云API密钥Key

### 依赖包

当前云函数暂未添加外部依赖，如需使用腾讯云SDK，需要在对应云函数的package.json中添加：

```json
{
  "dependencies": {
    "tencentcloud-sdk-nodejs": "^4.0.0"
  }
}
```

## 注意事项

1. **用户身份识别**: 微信云开发的OPENID在uniCloud中需要通过其他方式获取
2. **权限控制**: uniCloud使用不同的权限控制机制，需要配置数据库schema权限
3. **错误处理**: 统一使用uniCloud响应体规范
4. **环境配置**: 需要在uniCloud web控制台配置相关环境变量

## 下一步工作

1. 完成剩余云函数的创建
2. 配置uniCloud服务空间
3. 部署云函数到阿里云
4. 测试所有功能
5. 配置生产环境
