# uniCloud阿里云视频字幕翻译方案调整

## 方案概述

基于uniCloud阿里云的能力和限制，对原微信云开发+腾讯云VOD方案进行调整，保持核心功能不变的同时，优化架构设计以适配uniCloud环境。

## 核心架构调整

### 1. 技术栈变更

| 组件 | 原方案 | 调整后方案 | 变更原因 |
|------|--------|------------|----------|
| 后端服务 | 微信小程序云开发 | uniCloud阿里云 | 跨端支持、成本优化 |
| 云函数环境 | wx-server-sdk | uniCloud原生API | 平台迁移要求 |
| 用户身份识别 | 微信OPENID | uniCloud用户体系 | 平台差异 |
| 数据库操作 | 微信云数据库 | uniCloud数据库 | API差异调整 |
| 视频处理 | 腾讯云VOD | 腾讯云VOD (保持) | 核心功能保持 |

### 2. 架构优势

1. **跨端能力**: 支持小程序、H5、App等多端
2. **成本优化**: uniCloud阿里云版本成本更低
3. **开发效率**: 更好的开发工具和生态支持
4. **扩展性**: 更灵活的云函数配置和扩展能力

## 关键技术调整

### 1. 云函数调整

#### 1.1 上下文信息获取

**原方案（微信云开发）：**
```javascript
const { OPENID } = cloud.getWXContext()
```

**调整后（uniCloud）：**
```javascript
const { CLIENTIP, CLIENTUA } = context
// 用户身份通过其他方式识别，如设备ID或自定义用户系统
```

#### 1.2 数据库操作

**原方案（微信云开发）：**
```javascript
const cloud = require('wx-server-sdk')
const db = cloud.database()

await db.collection('tasks').add({
  data: { // 注意这里有data包装
    field1: value1
  }
})
```

**调整后（uniCloud）：**
```javascript
const db = uniCloud.database()

await db.collection('tasks').add({
  // 直接传入数据，无需data包装
  field1: value1
})
```

#### 1.3 错误处理规范

**调整后统一使用uniCloud响应体规范：**
```javascript
// 成功返回
{
  errCode: 0,
  errMsg: '操作成功',
  data: result
}

// 失败返回
{
  errCode: 'ERROR_CODE',
  errMsg: '错误描述'
}
```

### 2. 腾讯云VOD集成调整

#### 2.1 HTTP请求方式

**原方案：** 直接使用腾讯云SDK

**调整后：** 使用uniCloud的httpclient或固定IP代理

```javascript
// 使用uniCloud.httpclient发送请求
const response = await uniCloud.httpclient.request(apiUrl, {
  method: 'POST',
  data: requestData,
  contentType: 'json',
  dataType: 'json'
})

// 或使用阿里云固定IP代理（如需要）
const response = await uniCloud.httpProxyForEip.postJson(apiUrl, requestData)
```

#### 2.2 环境变量配置

在uniCloud web控制台配置环境变量：
- `TENCENT_SECRET_ID`
- `TENCENT_SECRET_KEY`
- `TENCENT_VOD_REGION`

### 3. 用户身份管理调整

#### 3.1 用户识别方案

由于无法直接获取微信OPENID，采用以下方案：

1. **设备指纹识别**：结合设备信息生成唯一标识
2. **临时用户系统**：为每个设备分配临时用户ID
3. **可选登录**：提供手机号登录等可选身份验证

```javascript
// 生成设备唯一标识
function generateDeviceId() {
  const systemInfo = uni.getSystemInfoSync()
  const deviceInfo = {
    platform: systemInfo.platform,
    model: systemInfo.model,
    system: systemInfo.system,
    version: systemInfo.version
  }
  return btoa(JSON.stringify(deviceInfo) + Date.now())
}
```

#### 3.2 数据库schema调整

```json
{
  "bsonType": "object",
  "properties": {
    "_id": {"description": "记录ID"},
    "deviceId": {"bsonType": "string", "description": "设备唯一标识"},
    "userId": {"bsonType": "string", "description": "用户ID（可选）"},
    "originalVideoFileId": {"bsonType": "string", "description": "原始视频FileId"},
    "processedVideoFileId": {"bsonType": "string", "description": "处理后视频FileId"},
    "status": {"bsonType": "string", "enum": ["processing", "completed", "failed"]},
    "createTime": {"bsonType": "timestamp", "description": "创建时间"},
    "finishTime": {"bsonType": "timestamp", "description": "完成时间"}
  }
}
```

## 数据流程调整

### 1. 视频上传流程

```mermaid
graph TD
    A[用户选择视频] --> B[生成设备ID]
    B --> C[调用get-upload-signature云函数]
    C --> D[获取VOD上传签名]
    D --> E[使用VOD SDK上传视频]
    E --> F[获取FileId]
    F --> G[调用create-task云函数]
    G --> H[创建任务记录]
    H --> I[VOD自动处理]
    I --> J[处理完成回调]
    J --> K[更新任务状态]
```

### 2. 任务状态查询

```mermaid
graph TD
    A[前端轮询] --> B[调用get-task-status]
    B --> C[查询数据库]
    C --> D{任务完成?}
    D -->|是| E[返回结果信息]
    D -->|否| F[返回处理中状态]
    E --> G[获取视频播放URL]
    F --> H[继续轮询]
```

## 性能优化策略

### 1. 云函数优化

- **内存配置**: 根据实际需求调整内存大小
- **超时设置**: 阿里云最长120秒，合理设置超时时间
- **并发控制**: 利用阿里云单实例多并发特性
- **冷启动优化**: 合并低频云函数，减少冷启动

### 2. 数据库优化

- **索引设计**: 为常用查询字段添加索引
- **分页查询**: 避免一次性查询大量数据
- **数据清理**: 定期清理过期任务记录

### 3. 网络优化

- **CDN加速**: 利用腾讯云VOD的CDN分发
- **请求合并**: 减少不必要的网络请求
- **缓存策略**: 合理使用本地缓存

## 安全性考虑

### 1. 数据安全

- **权限控制**: 通过schema配置数据访问权限
- **参数验证**: 严格验证输入参数
- **敏感信息**: 环境变量存储API密钥

### 2. 业务安全

- **频率限制**: 防止恶意上传和调用
- **文件校验**: 验证上传文件类型和大小
- **异常监控**: 监控异常请求和错误

## 部署和运维

### 1. 环境配置

1. 创建uniCloud阿里云服务空间
2. 配置环境变量
3. 部署云函数
4. 初始化数据库

### 2. 监控和日志

- **云函数日志**: 使用uniCloud.logger记录关键信息
- **错误监控**: 监控云函数执行错误
- **性能监控**: 关注响应时间和成功率

## 迁移风险评估

### 1. 技术风险

- **API兼容性**: 部分微信特有API需要替代方案
- **用户身份**: 用户识别方式变更可能影响用户体验
- **数据迁移**: 现有数据需要格式调整

### 2. 业务风险

- **功能完整性**: 确保核心功能不受影响
- **用户体验**: 保持操作流程的一致性
- **数据安全**: 确保用户数据安全迁移

### 3. 风险缓解

- **充分测试**: 在测试环境完整验证所有功能
- **灰度发布**: 逐步切换用户到新系统
- **回滚方案**: 准备快速回滚到原系统的方案
