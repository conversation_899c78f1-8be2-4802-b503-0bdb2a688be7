# 云函数部署说明

本项目需要部署以下云函数来支持视频字幕生成功能。

## 云函数列表

### 1. get-upload-signature
**功能**：获取腾讯云VOD上传签名
**触发方式**：客户端调用
**参数**：无
**返回**：上传签名字符串

### 2. create-task
**功能**：创建视频处理任务记录
**触发方式**：客户端调用
**参数**：
- `fileId`: VOD文件ID
**返回**：任务信息

### 3. handle-vod-callback (需要创建)
**功能**：处理VOD处理完成回调
**触发方式**：HTTP触发
**参数**：VOD回调数据
**返回**：处理结果

### 4. get-task-status (需要创建)
**功能**：获取任务处理状态
**触发方式**：客户端调用
**参数**：
- `fileId`: 原始文件ID
**返回**：任务状态信息

### 5. get-video-url (需要创建)
**功能**：获取视频播放URL
**触发方式**：客户端调用
**参数**：
- `fileId`: 处理后的文件ID
**返回**：播放URL

## 部署步骤

### 1. 开通云开发
1. 在微信开发者工具中开通云开发
2. 创建云环境（建议创建两个环境：开发环境和生产环境）

### 2. 配置环境变量
在云开发控制台设置以下环境变量：
- `TENCENT_SECRET_ID`: 腾讯云API密钥ID
- `TENCENT_SECRET_KEY`: 腾讯云API密钥Key

### 3. 部署云函数
在微信开发者工具中：
1. 右键点击云函数文件夹
2. 选择"上传并部署：云端安装依赖"
3. 等待部署完成

### 4. 配置数据库
创建以下集合：
- `tasks`: 存储任务信息

### 5. 配置权限
确保云函数有访问数据库的权限。

## 开发注意事项

### 1. 本地调试
- 使用微信开发者工具的云函数本地调试功能
- 可以在本地模拟云函数执行

### 2. 日志查看
- 在云开发控制台查看云函数执行日志
- 用于排查问题和性能监控

### 3. 版本管理
- 建议为每个云函数创建版本标签
- 便于回滚和管理

## 完整云函数实现

以下是需要补充实现的云函数：

### handle-vod-callback/index.js
```javascript
const cloud = require('wx-server-sdk')
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })
const db = cloud.database()

exports.main = async (event, context) => {
  // 处理VOD回调逻辑
  // 更新任务状态
}
```

### get-task-status/index.js
```javascript
const cloud = require('wx-server-sdk')
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })
const db = cloud.database()

exports.main = async (event, context) => {
  // 查询任务状态逻辑
}
```

### get-video-url/index.js
```javascript
const cloud = require('wx-server-sdk')
cloud.init({ env: cloud.DYNAMIC_CURRENT_ENV })

exports.main = async (event, context) => {
  // 获取视频播放URL逻辑
}
```

## 安全注意事项

1. **API密钥安全**：绝不要在代码中硬编码API密钥
2. **权限控制**：确保云函数只能被授权用户调用
3. **数据验证**：对输入参数进行严格验证
4. **错误处理**：妥善处理异常情况，避免敏感信息泄露

## 监控和维护

1. **性能监控**：定期查看云函数执行时间和成功率
2. **日志分析**：分析错误日志，及时修复问题
3. **资源使用**：监控云函数资源使用情况，避免超出配额

---

详细的云函数实现请参考需求文档中的技术方案。
