// uniCloud云函数：创建任务记录
'use strict';

/**
 * 创建视频处理任务记录
 */
exports.main = async (event, context) => {
  try {
    const { fileId } = event
    const { CLIENTUA, CLIENTIP } = context
    
    if (!fileId) {
      return {
        errCode: 'INVALID_PARAM',
        errMsg: 'fileId参数不能为空'
      }
    }
    
    // 获取数据库引用
    const db = uniCloud.database()
    
    // 创建任务记录
    const taskData = {
      originalVideoFileId: fileId,
      status: 'processing',
      createTime: new Date(),
      fileName: `video_${Date.now()}.mp4`,
      fileSize: 0, // 实际开发中从VOD获取
      duration: 0,  // 实际开发中从VOD获取
      clientIP: CLIENTIP,
      userAgent: CLIENTUA
    }
    
    const result = await db.collection('tasks').add(taskData)
    
    console.log('任务创建成功:', result)
    
    return {
      errCode: 0,
      errMsg: '创建成功',
      taskId: result.id,
      ...taskData
    }
    
  } catch (error) {
    console.error('创建任务失败:', error)
    return {
      errCode: 'CREATE_TASK_FAILED',
      errMsg: '创建任务失败: ' + error.message
    }
  }
}
