# uniCloud阿里云技术要点总结

## uniCloud概述

uniCloud是DCloud联合阿里云、腾讯云、支付宝云提供的基于serverless模式和js编程的云开发平台。本项目选择阿里云版本。

## 核心优势

1. **跨端支持**: 支持App、H5、各家小程序等全端应用
2. **跨云支持**: 支持阿里云、腾讯云，方便切换
3. **开发效率**: 提供clientDB、JQL、schema2code等高效开发工具
4. **成本优势**: serverless按量计费，无需关心服务器运维

## 云函数开发规范

### 1. 基本结构

```javascript
'use strict';

exports.main = async (event, context) => {
  try {
    // 业务逻辑
    return {
      errCode: 0,
      errMsg: '操作成功',
      data: {}
    }
  } catch (error) {
    return {
      errCode: 'ERROR_CODE',
      errMsg: error.message
    }
  }
}
```

### 2. 上下文信息

- `event`: 客户端传入的参数
- `context`: 包含客户端信息
  - `CLIENTIP`: 客户端IP
  - `CLIENTUA`: 客户端User-Agent
  - `requestId`: 请求ID

### 3. 响应体规范

```javascript
// 成功响应
{
  errCode: 0,
  errMsg: '操作成功',
  data: {},
  newToken: {} // 可选，用于token续期
}

// 错误响应
{
  errCode: 'ERROR_CODE',
  errMsg: '错误描述'
}
```

## 数据库操作

### 1. 基本用法

```javascript
const db = uniCloud.database()
const collection = db.collection('collectionName')

// 查询
const result = await collection.where({}).get()

// 添加
const addResult = await collection.add({})

// 更新
const updateResult = await collection.doc('id').update({})

// 删除
const deleteResult = await collection.doc('id').remove()
```

### 2. JQL语法

uniCloud支持JQL（JavaScript Query Language），简化数据库操作：

```javascript
const db = uniCloud.databaseJQL()

// 联表查询
const result = await db.collection('orders')
  .lookup({
    from: 'users',
    localField: 'user_id',
    foreignField: '_id',
    as: 'userInfo'
  })
  .get()
```

### 3. Schema设计

数据库表需要定义schema文件，包含：
- 字段定义和类型
- 数据验证规则
- 权限控制
- 索引配置

## 云函数配置

### package.json配置

```json
{
  "name": "function-name",
  "cloudfunction-config": {
    "memorySize": 256,
    "timeout": 10,
    "runtime": "Nodejs16"
  }
}
```

### 配置项说明

- `memorySize`: 内存大小(MB)，默认256
- `timeout`: 超时时间(秒)，阿里云最长120秒
- `runtime`: Node.js版本，支持Nodejs16/18/20

## 客户端API调用

### 1. 云函数调用

```javascript
uniCloud.callFunction({
  name: 'functionName',
  data: {},
  success: (res) => {
    console.log(res.result)
  },
  fail: (error) => {
    console.error(error)
  }
})
```

### 2. 云对象调用（推荐）

```javascript
const cloudObject = uniCloud.importObject('objectName')
const result = await cloudObject.methodName(params)
```

### 3. clientDB（直接操作数据库）

```javascript
const db = uniCloud.database()
const result = await db.collection('tableName').where({}).get()
```

## 阿里云特性

### 1. 单实例多并发

- 支持配置单实例并发度(1-100)
- 可减少冷启动次数
- 需注意全局变量污染问题

### 2. 固定出口IP

- 使用`uniCloud.httpProxyForEip`发送请求
- 支持微信公众号等需要固定IP的场景
- 仅支持特定域名代理

### 3. 时区设置

- 阿里云使用UTC+0时区
- 建议统一使用时间戳避免时区问题

## 最佳实践

### 1. 错误处理

```javascript
try {
  // 业务逻辑
} catch (error) {
  console.error('操作失败:', error)
  return {
    errCode: 'OPERATION_FAILED',
    errMsg: error.message
  }
}
```

### 2. 参数验证

```javascript
exports.main = async (event, context) => {
  const { param1, param2 } = event
  
  if (!param1) {
    return {
      errCode: 'INVALID_PARAM',
      errMsg: 'param1不能为空'
    }
  }
  
  // 业务逻辑
}
```

### 3. 数据库查询优化

- 合理使用索引
- 避免全表扫描
- 使用分页查询
- 利用JQL简化复杂查询

### 4. 云函数设计

- 单一职责原则
- 合理控制函数体积(<10MB)
- 避免长时间运行的任务
- 使用公共模块复用代码

## 迁移注意事项

1. **用户身份**: 微信云开发的OPENID需要其他方式获取
2. **数据格式**: 注意add/update操作参数差异
3. **权限控制**: 使用schema配置权限而非微信账户体系
4. **环境变量**: 在uniCloud控制台配置敏感信息
5. **依赖管理**: 阿里云支持云端安装依赖

## 开发工具

- **HBuilderX**: 官方IDE，支持云函数开发调试
- **uniCloud控制台**: Web管理界面，管理服务空间
- **本地调试**: 支持本地运行云函数进行调试
