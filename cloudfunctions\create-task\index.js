// 云函数：创建任务记录
const cloud = require('wx-server-sdk')

// 初始化云开发环境
cloud.init({
  env: cloud.DYNAMIC_CURRENT_ENV
})

const db = cloud.database()

/**
 * 创建视频处理任务记录
 */
exports.main = async (event, context) => {
  try {
    const { fileId } = event
    const { OPENID } = cloud.getWXContext()
    
    if (!fileId) {
      throw new Error('fileId参数不能为空')
    }
    
    // 创建任务记录
    const taskData = {
      _openid: OPENID,
      originalVideoFileId: fileId,
      status: 'processing',
      createTime: new Date(),
      fileName: `video_${Date.now()}.mp4`,
      fileSize: 0, // 实际开发中从VOD获取
      duration: 0  // 实际开发中从VOD获取
    }
    
    const result = await db.collection('tasks').add({
      data: taskData
    })
    
    console.log('任务创建成功:', result)
    
    return {
      taskId: result._id,
      ...taskData
    }
    
  } catch (error) {
    console.error('创建任务失败:', error)
    throw new Error('创建任务失败: ' + error.message)
  }
}
