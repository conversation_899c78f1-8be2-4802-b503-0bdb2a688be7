# uniCloud阿里云部署指南

## 部署前准备

### 1. 环境要求

- HBuilderX 3.0+
- Node.js 16+
- 腾讯云VOD服务账号
- uniCloud阿里云服务空间

### 2. 账号准备

#### 2.1 uniCloud服务空间

1. 登录 [uniCloud控制台](https://unicloud.dcloud.net.cn)
2. 创建新的服务空间，选择阿里云
3. 记录服务空间ID和相关信息

#### 2.2 腾讯云VOD配置

1. 开通腾讯云VOD服务
2. 获取API密钥：
   - 登录腾讯云控制台
   - 进入 [API密钥管理](https://console.cloud.tencent.com/cam/capi)
   - 创建或获取 `SecretId` 和 `SecretKey`

3. 配置VOD任务流：
   - 进入VOD控制台 -> 视频处理设置 -> 任务流模板
   - 创建名为 `SubtitleProcessing` 的任务流
   - 添加ASR语音识别任务
   - 添加转码并烧录字幕任务

## 部署步骤

### 第一步：项目配置

#### 1.1 关联服务空间

1. 在HBuilderX中打开项目
2. 右键点击 `uniCloud-aliyun` 文件夹
3. 选择"关联云服务空间"
4. 选择或创建阿里云服务空间

#### 1.2 配置环境变量

在uniCloud web控制台配置以下环境变量：

```
TENCENT_SECRET_ID=你的腾讯云SecretId
TENCENT_SECRET_KEY=你的腾讯云SecretKey
TENCENT_VOD_REGION=ap-guangzhou
```

### 第二步：数据库初始化

#### 2.1 创建数据表

1. 在uniCloud控制台进入数据库管理
2. 创建集合 `tasks`
3. 上传schema文件 `uniCloud-aliyun/database/tasks.schema.json`

#### 2.2 配置索引

为以下字段创建索引以优化查询性能：
- `deviceId` (单字段索引)
- `originalVideoFileId` (单字段索引)
- `status` (单字段索引)
- `createTime` (单字段索引，降序)

### 第三步：云函数部署

#### 3.1 部署所有云函数

在HBuilderX中：
1. 右键点击 `uniCloud-aliyun/cloudfunctions` 文件夹
2. 选择"上传所有云函数"
3. 等待部署完成

#### 3.2 配置HTTP触发

为 `handle-vod-callback` 云函数配置HTTP触发：
1. 在uniCloud控制台找到该云函数
2. 配置HTTP触发器
3. 记录生成的HTTP URL

#### 3.3 云函数列表确认

确保以下云函数部署成功：
- [x] `create-task` - 创建任务记录
- [x] `get-upload-signature` - 获取VOD上传签名
- [x] `get-task-status` - 获取任务状态
- [x] `get-task-detail` - 获取任务详情
- [x] `get-video-url` - 获取视频播放URL
- [x] `get-history-list` - 获取历史记录列表
- [x] `get-recent-tasks` - 获取最近任务
- [x] `handle-vod-callback` - 处理VOD回调

### 第四步：VOD回调配置

#### 4.1 配置回调URL

1. 登录腾讯云VOD控制台
2. 进入"回调设置"
3. 设置回调模式为"可靠回调"
4. 回调URL填入 `handle-vod-callback` 云函数的HTTP URL
5. 勾选"任务流状态变更"事件

#### 4.2 测试回调

可以通过VOD控制台的测试功能验证回调配置是否正确。

### 第五步：前端配置

#### 5.1 安装依赖

```bash
npm install vod-wx-sdk-v2
```

#### 5.2 配置manifest.json

确保小程序配置正确：
```json
{
  "mp-weixin": {
    "appid": "你的小程序AppID",
    "setting": {
      "urlCheck": false
    }
  }
}
```

### 第六步：测试验证

#### 6.1 功能测试清单

- [ ] 视频上传功能
- [ ] 任务创建和状态查询
- [ ] VOD处理流程
- [ ] 回调处理
- [ ] 视频播放URL获取
- [ ] 历史记录查询

#### 6.2 测试步骤

1. **上传测试**：
   - 选择一个短视频文件
   - 执行上传流程
   - 确认任务创建成功

2. **处理测试**：
   - 等待VOD处理完成
   - 检查任务状态更新
   - 验证回调处理

3. **播放测试**：
   - 获取处理后的视频URL
   - 在video组件中播放
   - 确认字幕正确显示

## 监控和维护

### 1. 日志监控

- 在uniCloud控制台查看云函数执行日志
- 监控错误率和响应时间
- 设置告警规则

### 2. 性能优化

- 监控云函数内存使用情况
- 优化数据库查询
- 调整云函数配置

### 3. 安全检查

- 定期检查环境变量配置
- 监控异常访问
- 更新API密钥

## 故障排除

### 常见问题

1. **云函数调用失败**
   - 检查服务空间关联
   - 确认云函数部署状态
   - 查看错误日志

2. **VOD回调失败**
   - 检查回调URL配置
   - 确认云函数HTTP触发器
   - 查看VOD控制台日志

3. **视频处理失败**
   - 检查VOD任务流配置
   - 确认API密钥正确
   - 查看VOD处理日志

### 调试技巧

1. **本地调试**：
   - 使用HBuilderX本地运行云函数
   - 查看控制台输出
   - 使用断点调试

2. **线上调试**：
   - 查看uniCloud控制台日志
   - 使用console.log输出关键信息
   - 监控数据库变更

## 版本更新

### 更新流程

1. 备份当前配置
2. 更新代码
3. 测试新功能
4. 部署到生产环境
5. 验证功能正常

### 回滚方案

如果更新出现问题，可以：
1. 回滚云函数到上一版本
2. 恢复数据库配置
3. 检查并修复问题
4. 重新部署
