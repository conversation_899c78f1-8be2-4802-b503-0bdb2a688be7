<template>
  <view class="upload-container">
    <view class="upload-area" @click="chooseVideo">
      <view class="upload-icon">
        <text class="iconfont">📹</text>
      </view>
      <view class="upload-text">
        <text class="main-text">点击选择视频</text>
        <text class="sub-text">支持MP4、MOV等格式，最大100MB</text>
      </view>
    </view>
    
    <!-- 视频预览 -->
    <view v-if="videoInfo" class="video-preview">
      <video 
        :src="videoInfo.tempFilePath" 
        controls 
        class="preview-video"
        :poster="videoInfo.thumbTempFilePath"
      ></video>
      <view class="video-info">
        <text>文件大小: {{ formatFileSize(videoInfo.size) }}</text>
        <text>时长: {{ formatDuration(videoInfo.duration) }}</text>
      </view>
    </view>
    
    <!-- 上传进度 -->
    <view v-if="uploading" class="upload-progress">
      <view class="progress-bar">
        <view class="progress-fill" :style="{ width: uploadPercent + '%' }"></view>
      </view>
      <text class="progress-text">上传中... {{ uploadPercent }}%</text>
    </view>
    
    <!-- 操作按钮 -->
    <view class="action-buttons">
      <button 
        v-if="videoInfo && !uploading" 
        @click="startUpload" 
        class="upload-btn"
        type="primary"
      >
        开始上传
      </button>
      <button 
        v-if="videoInfo && !uploading" 
        @click="resetVideo" 
        class="reset-btn"
      >
        重新选择
      </button>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import VodUploader from 'vod-wx-sdk-v2'
import { formatFileSize, formatDuration } from '@/utils/common'
import { getUploadSignature as getSignature, createTask as createTaskRecord } from '@/utils/api'

// 响应式数据
const videoInfo = ref<any>(null)
const uploading = ref(false)
const uploadPercent = ref(0)

// 选择视频
const chooseVideo = () => {
  uni.chooseVideo({
    sourceType: ['camera', 'album'],
    maxDuration: 300, // 最大5分钟
    success: (res) => {
      console.log('选择视频成功:', res)
      videoInfo.value = res
    },
    fail: (err) => {
      console.error('选择视频失败:', err)
      uni.showToast({
        title: '选择视频失败',
        icon: 'none'
      })
    }
  })
}

// 开始上传
const startUpload = async () => {
  if (!videoInfo.value) return
  
  try {
    uploading.value = true
    uploadPercent.value = 0
    
    // 获取上传签名
    const signature = await getUploadSignature()
    
    // 使用VOD SDK上传
    const uploader = VodUploader.start({
      mediaFile: videoInfo.value,
      getSignature: () => signature,
      procedure: 'SubtitleProcessing', // 任务流名称
      onProgress: (progress: any) => {
        uploadPercent.value = Math.round(progress.percent * 100)
      },
      onFinish: async (result: any) => {
        console.log('上传完成:', result)
        
        // 创建任务记录
        await createTask(result.fileId)
        
        // 跳转到处理页面
        uni.navigateTo({
          url: `/pages/process/process?fileId=${result.fileId}`
        })
      },
      onError: (error: any) => {
        console.error('上传失败:', error)
        uni.showToast({
          title: '上传失败',
          icon: 'none'
        })
        uploading.value = false
      }
    })
  } catch (error) {
    console.error('上传过程出错:', error)
    uni.showToast({
      title: '上传失败',
      icon: 'none'
    })
    uploading.value = false
  }
}

// 获取上传签名
const getUploadSignature = async (): Promise<string> => {
  return await getSignature()
}

// 创建任务记录
const createTask = async (fileId: string) => {
  return await createTaskRecord(fileId)
}

// 重置视频
const resetVideo = () => {
  videoInfo.value = null
  uploading.value = false
  uploadPercent.value = 0
}

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (size < 1024) return size + 'B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + 'KB'
  return (size / (1024 * 1024)).toFixed(1) + 'MB'
}

// 格式化时长
const formatDuration = (duration: number): string => {
  const minutes = Math.floor(duration / 60)
  const seconds = Math.floor(duration % 60)
  return `${minutes}:${seconds.toString().padStart(2, '0')}`
}
</script>

<style scoped>
.upload-container {
  padding: 40rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.upload-area {
  background: white;
  border: 2rpx dashed #007AFF;
  border-radius: 20rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  margin-bottom: 40rpx;
}

.upload-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
}

.upload-text .main-text {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 10rpx;
}

.upload-text .sub-text {
  display: block;
  font-size: 24rpx;
  color: #999;
}

.video-preview {
  background: white;
  border-radius: 20rpx;
  padding: 20rpx;
  margin-bottom: 40rpx;
}

.preview-video {
  width: 100%;
  height: 400rpx;
  border-radius: 10rpx;
}

.video-info {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
  font-size: 24rpx;
  color: #666;
}

.upload-progress {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.progress-fill {
  height: 100%;
  background-color: #007AFF;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 28rpx;
  color: #333;
  text-align: center;
  display: block;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.upload-btn {
  flex: 1;
  background-color: #007AFF;
  color: white;
  border: none;
  border-radius: 10rpx;
  height: 88rpx;
  font-size: 32rpx;
}

.reset-btn {
  flex: 1;
  background-color: white;
  color: #007AFF;
  border: 2rpx solid #007AFF;
  border-radius: 10rpx;
  height: 88rpx;
  font-size: 32rpx;
}
</style>
